cd ~/IsaacLab

# ./isaaclab.sh -p scripts/environments/teleoperation/teleop_se3_agent.py \
# --task <PERSON>-<PERSON><PERSON>-<PERSON><PERSON>-<PERSON><PERSON>-IK-Rel-v0 \
# --num_envs 1 \
# --teleop_device keyboard

# ./isaaclab.sh -p scripts/environments/teleoperation/teleop_se3_agent.py \
# --task <PERSON>-<PERSON><PERSON>-<PERSON><PERSON>-<PERSON><PERSON><PERSON>-IK-Rel-v0 \
# --num_envs 1 \
# --teleop_device keyboard

./isaaclab.sh -p scripts/environments/teleoperation/teleop_se3_agent.py \
--task <PERSON>-Lift-<PERSON>ube-ZLZK-IK-Rel-v0 \
--num_envs 1 \
--teleop_device keyboard